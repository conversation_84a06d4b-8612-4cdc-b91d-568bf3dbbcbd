import {
  Authenticator,
  ThemeProvider,
  defaultDarkModeOverride,
} from '@aws-amplify/ui-react-native';
import { I18n } from 'aws-amplify/utils';
import axios from 'axios';
import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { ActivityIndicator, Image } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../src/components/Text';
import { useAuth } from '../src/context/AuthContext';
import { useTheme } from '../src/theme/ThemeContext';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 0;
  width: 100%;
`;
styled(Text)`
  font-size: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 32px;
`;

const Title = styled(Text)`
  font-size: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-top: 32px;
  text-align: center;
`;
const Subtitle = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
  text-align: center;
`;
const Strong = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

// Custom header for the Authenticator
const AuthHeader = ({ remaining }: { remaining: number }) => {
  return (
    <>
      <Title>Welcome to the Jumpers for Goalposts Closed Alpha</Title>
      <Image
        source={require('../assets/logo.png')}
        style={{
          width: 400,
          height: 300,
          resizeMode: 'contain',
          marginBottom: 0,
          alignSelf: 'center',
        }}
      />
      <Subtitle>
        There are currently <Strong>{remaining}</Strong> spaces left in the alpha test.
      </Subtitle>
    </>
  );
};

// Override the default Authenticator styles
I18n.putVocabulariesForLanguage('en', {
  'Sign In': 'Login',
  'Sign in': 'Login',
  'Sign in to your account': 'Welcome Back!',
  Username: 'Email',
  'Enter your Username': 'Enter your email',
  'Enter your username': 'Enter your email',
});

const LoginScreen = () => {
  const { isLoading, isAuthenticated } = useAuth();
  const { theme: appTheme, isDark } = useTheme();
  const [remaining, setRemaining] = React.useState(0);
  const [loadingCount, setLoadingCount] = React.useState(true);
  useEffect(() => {
    async function getTeamCount() {
      try {
        const res = await axios.get<{ remaining: number }>(
          `${process.env.EXPO_PUBLIC_API_URL}/available-teams`
        );
        setRemaining(res.data.remaining);
        setLoadingCount(false);
      } catch (error) {
        console.log('Error fetching team count:', error);
      }
    }
    getTeamCount();
  }, []);

  // If we're already authenticated and we navigate here, redirect to the app
  useEffect(() => {
    if (isAuthenticated) {
      // Redirect to the app's home screen
      // The app layout will handle checking for notification settings
      router.replace('/');
    }
  }, [isAuthenticated]);

  if (isLoading || loadingCount) {
    return (
      <Container>
        <ActivityIndicator size="large" color={appTheme.colors.primary} />
      </Container>
    );
  }

  return (
    <Container>
      <ThemeProvider
        colorMode={isDark ? 'dark' : 'light'}
        theme={{
          tokens: {
            colors: {
              background: {
                value: appTheme.colors.background,
              },
            },
          },
          overrides: [defaultDarkModeOverride],
        }}
      >
        <Authenticator
          socialProviders={['google']}
          loginMechanism="email"
          Header={(props) => <AuthHeader remaining={remaining} {...props} />}
        />
      </ThemeProvider>
    </Container>
  );
};

export default LoginScreen;
