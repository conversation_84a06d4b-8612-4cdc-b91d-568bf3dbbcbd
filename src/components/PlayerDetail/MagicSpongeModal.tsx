import { useRouter } from 'expo-router';
import React, { useCallback } from 'react';
import { Image, Modal } from 'react-native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { Player } from '../../models/player';
import { AD_TYPES, useRewardAd } from '../Ads';
import { MagicSpongeActions, MagicSpongeState } from './hooks/useMagicSponge';
import {
  ButtonText,
  DetailText,
  ModalButton,
  ModalButtonContainer,
  ModalContainer,
  ModalContent,
  ModalPill,
  ModalPillText,
  ModalTitle,
  ModalTitleRow,
  TransferButton,
} from './ModalSharedComponents';

interface MagicSpongeModalProps {
  player: Player;
  isInjured: boolean;
  spongesAvailable: number;
  magicSpongeState: MagicSpongeState;
  magicSpongeActions: MagicSpongeActions;
}

export const MagicSpongeModal: React.FC<MagicSpongeModalProps> = ({
  player,
  isInjured,
  spongesAvailable,
  magicSpongeState,
  magicSpongeActions,
}) => {
  const { isAdLoaded, showAd } = useRewardAd();
  const { playerActions } = usePlayerUtils();
  const router = useRouter();

  const handleMagicSpongeUse = async () => {
    await magicSpongeActions.useMagicSponge(player as Player, (_title, _message) => {
      playerActions.setShowAlert(true);
    });
  };

  const rewardEarned = useCallback(() => {
    magicSpongeActions.incrementMagicSpongeCount();
  }, [magicSpongeActions]);

  console.log('MagicSpongeModal rendered', {
    playerId: player.playerId,
    isInjured,
    spongesAvailable,
    isSpongeModalVisible: magicSpongeState.isSpongeModalVisible,
  });

  const spongeReason = isInjured
    ? `Use the magic sponge to reduce the injury duration by 1 day`
    : `Use the magic sponge to restore ${player.firstName} ${player.surname} to full energy`;

  return (
    <Modal
      visible={magicSpongeState.isSpongeModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => magicSpongeActions.setIsSpongeModalVisible(false)}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitleRow>
            <ModalTitle>Magic Sponge</ModalTitle>
            <ModalPill>
              <Image
                source={require('../../../assets/magicSponge.png')}
                style={{ width: 24, height: 24 }}
              />
              <ModalPillText>{spongesAvailable}</ModalPillText>
            </ModalPill>
          </ModalTitleRow>
          <DetailText>{spongeReason}</DetailText>
          <ModalButtonContainer>
            <ModalButton
              disabled={spongesAvailable === 0 || magicSpongeState.isSpongeLoading}
              onPress={handleMagicSpongeUse}
              style={{
                opacity: spongesAvailable === 0 || magicSpongeState.isSpongeLoading ? 0.5 : 1,
              }}
            >
              <ButtonText>
                {magicSpongeState.isSpongeLoading ? 'Using...' : 'Use magic sponge'}
              </ButtonText>
            </ModalButton>
            <ModalButton
              disabled={!isAdLoaded(AD_TYPES.MAGIC_SPONGE)}
              onPress={async () => await showAd(AD_TYPES.MAGIC_SPONGE, rewardEarned)}
              style={{ opacity: !isAdLoaded(AD_TYPES.MAGIC_SPONGE) ? 0.5 : 1 }}
            >
              <ButtonText>Watch ad</ButtonText>
            </ModalButton>
            <ModalButton
              onPress={() => {
                router.push('/club-shop');
                magicSpongeActions.setIsSpongeModalVisible(false)
              }}
            >
              <ButtonText>Buy more</ButtonText>
            </ModalButton>
          </ModalButtonContainer>
          <TransferButton
            onPress={() => magicSpongeActions.setIsSpongeModalVisible(false)}
            style={{ marginTop: 16 }}
          >
            <ButtonText style={{ color: '#333' }}>Close</ButtonText>
          </TransferButton>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
