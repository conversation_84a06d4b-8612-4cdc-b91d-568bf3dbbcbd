import React, { useCallback } from 'react';
import { Image, Modal } from 'react-native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { Player } from '../../models/player';
import { AD_TYPES, useRewardAd } from '../Ads';
import { RedCardAppealActions, RedCardAppealState } from './hooks/useRedCardAppeal';
import {
  ButtonText,
  DetailText,
  ModalButton,
  ModalButtonContainer,
  ModalContainer,
  ModalContent,
  ModalPill,
  ModalPillText,
  ModalTitle,
  ModalTitleRow,
  TransferButton,
} from './ModalSharedComponents';

interface RedCardAppealModalProps {
  player: Player;
  isSuspended: boolean;
  appealsAvailable: number;
  redCardAppealState: RedCardAppealState;
  redCardAppealActions: RedCardAppealActions;
}

export const RedCardAppealModal: React.FC<RedCardAppealModalProps> = ({
  player,
  isSuspended,
  appealsAvailable,
  redCardAppealState,
  redCardAppealActions,
}) => {
  const { isAdLoaded, showAd } = useRewardAd();
  const { playerActions } = usePlayerUtils();

  const handleRedCardAppealUse = async () => {
    await redCardAppealActions.useRedCardAppeal(player as Player, (_title, _message) => {
      playerActions.setShowAlert(true);
    });
  };

  const rewardEarned = useCallback(() => {
    redCardAppealActions.incrementRedCardAppealCount();
  }, [redCardAppealActions]);

  console.log('RedCardAppealModal rendered', {
    playerId: player.playerId,
    isSuspended,
    appealsAvailable,
    isAppealModalVisible: redCardAppealState.isAppealModalVisible,
  });

  const appealReason = `Use the red card appeal to reduce ${player.firstName} ${player.surname}'s suspension by 1 game`;

  return (
    <Modal
      visible={redCardAppealState.isAppealModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => redCardAppealActions.setIsAppealModalVisible(false)}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitleRow>
            <ModalTitle>Red Card Appeal</ModalTitle>
            <ModalPill>
              <Image
                source={require('../../../assets/redcard.png')}
                style={{ resizeMode: 'contain', width: 24, height: 24 }}
              />
              <ModalPillText>{appealsAvailable}</ModalPillText>
            </ModalPill>
          </ModalTitleRow>
          <DetailText>{appealReason}</DetailText>
          <ModalButtonContainer>
            <ModalButton
              disabled={appealsAvailable === 0 || redCardAppealState.isAppealLoading}
              onPress={handleRedCardAppealUse}
              style={{
                opacity: appealsAvailable === 0 || redCardAppealState.isAppealLoading ? 0.5 : 1,
              }}
            >
              <ButtonText>
                {redCardAppealState.isAppealLoading ? 'Appealing...' : 'Use red card appeal'}
              </ButtonText>
            </ModalButton>
            <ModalButton
              disabled={!isAdLoaded(AD_TYPES.RED_CARD_APPEAL)}
              onPress={() => showAd(AD_TYPES.RED_CARD_APPEAL, rewardEarned)}
              style={{ opacity: !isAdLoaded(AD_TYPES.RED_CARD_APPEAL) ? 0.5 : 1 }}
            >
              <ButtonText>Watch ad</ButtonText>
            </ModalButton>
            <ModalButton
              onPress={() => {
                router.push('/club-shop');
                magicSpongeActions.setIsSpongeModalVisible(false);
              }}
            >
              <ButtonText>Buy more</ButtonText>
            </ModalButton>
          </ModalButtonContainer>
          <TransferButton
            onPress={() => redCardAppealActions.setIsAppealModalVisible(false)}
            style={{ marginTop: 16 }}
          >
            <ButtonText style={{ color: '#333' }}>Close</ButtonText>
          </TransferButton>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
